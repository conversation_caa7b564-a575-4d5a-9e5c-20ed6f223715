from fastapi import APIRouter, status, Response
from typing import Optional, List
from src.schemas.whats_app import WhatsAppSessionCreate, WhatsAppSessionOut
from src.services.whats_app import (
    create_whatsapp_session, get_qr_for_session,
    get_whatsapp_session_status, delete_whatsapp_session,
    connect_whatsapp_session, disconnect_whatsapp_session,
    get_user_session_by_id, get_all_user_sessions, set_active_session,
)
from src.routes.deps import SessionDep, CurrentUserUpgrade
from src.utils.handle_router_exceptions import handle_router_exceptions

whats_app = APIRouter(prefix="/whatsapp", tags=["WhatsApp Sessions"])

@whats_app.post("/sessions/", response_model=WhatsAppSessionOut)
@handle_router_exceptions
async def create_session(
    data: WhatsAppSessionCreate,
    db: SessionDep,
    user_id: CurrentUserUpgrade
):
    return await create_whatsapp_session(user_id, db, data)

@whats_app.get("/sessions/", response_model=List[WhatsAppSessionOut])
@handle_router_exceptions
async def list_user_sessions(
    db: SessionDep,
    user_id: CurrentUserUpgrade
):
    return await get_all_user_sessions(user_id, db)

@whats_app.get("/sessions/{session_id}", response_model=Optional[WhatsAppSessionOut])
@handle_router_exceptions
async def get_one_user_session(
    session_id: str,
    db: SessionDep,
    user_id: CurrentUserUpgrade
):
    return await get_user_session_by_id(user_id, session_id, db)

@whats_app.get("/sessions/{session_id}/qr", response_model=Optional[str])
@handle_router_exceptions
async def qr(
    session_id: str,
    db: SessionDep,
    user_id: CurrentUserUpgrade
):
    # Will raise if session not found or QR not available
    await get_user_session_by_id(user_id, session_id, db)  # Ownership check
    return await get_qr_for_session(db, session_id)

@whats_app.get("/sessions/{session_id}/status", response_model=Optional[str])
@handle_router_exceptions
async def status(
    session_id: str,
    db: SessionDep,
    user_id: CurrentUserUpgrade
):
    await get_user_session_by_id(user_id, session_id, db)  # Ownership check
    return await get_whatsapp_session_status(db, session_id)

@whats_app.post("/sessions/{session_id}/connect", response_model=Optional[WhatsAppSessionOut])
@handle_router_exceptions
async def connect(
    session_id: str,
    db: SessionDep,
    user_id: CurrentUserUpgrade
):
    await get_user_session_by_id(user_id, session_id, db)
    return await connect_whatsapp_session(db, session_id)

@whats_app.post("/sessions/{session_id}/disconnect", response_model=Optional[WhatsAppSessionOut])
@handle_router_exceptions
async def disconnect(
    session_id: str,
    db: SessionDep,
    user_id: CurrentUserUpgrade
):
    await get_user_session_by_id(user_id, session_id, db)
    return await disconnect_whatsapp_session(db, session_id)

@whats_app.put("/sessions/{session_id}/set-active", status_code=status.HTTP_204_NO_CONTENT)
@handle_router_exceptions
async def activate_session(
    session_id: str,
    db: SessionDep,
    user_id: CurrentUserUpgrade
):
    await set_active_session(user_id, db, session_id)
    return Response(status_code=status.HTTP_204_NO_CONTENT)

@whats_app.delete("/sessions/{session_id}", response_model=bool)
@handle_router_exceptions
async def delete(
    session_id: str,
    db: SessionDep,
    user_id: CurrentUserUpgrade
):
    await get_user_session_by_id(user_id, session_id, db)
    return await delete_whatsapp_session(db, session_id)

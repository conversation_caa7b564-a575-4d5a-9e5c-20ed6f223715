# app/DB/__init__.py
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession

__all__ = [
    "Base", "AsyncSessionLocal", "get_db",
    "User", "UserAuth", "Country", "RefreshToken", "ContactUsSubmission",
    "Assessment", "Dimension", "Question", "Option", "ResultProfile",
    "UserSession", "Response", "SessionProfile",
]

from src.utils import TsLogger
from src.DB.database import Base, AsyncSessionLocal
from src.DB.models.users import User, UserAuth, Country, RefreshToken, ContactUsSubmission
from src.DB.models.assessments import (Assessment, Dimension, Question,
                                                       Option, ResultProfile, UserSession, Response, SessionProfile)


logger = TsLogger(name=__name__)


# Dependency function to provide a database session for FastAPI
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            raise e
        finally:
            await session.close()

from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request
from starlette.exceptions import HTTPException as StarletteHTTPException

from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import SQLAlchemyError
from jose import JW<PERSON>rror, ExpiredSignatureError
from pydantic import ValidationError
import traceback
from functools import wraps
from typing import Callable
from .logger import TsLogger
from src.config import settings

def handle_router_exceptions(func: Callable):
    @wraps(func)
    async def wrapper(request: Request, *args, **kwargs):
        logger = TsLogger(f"{func.__module__}.{func.__name__}")

        try:
            return await func(request, *args, **kwargs)

        except (HTTPException, StarletteHTTPException)  as e:
            if settings.ENVIRONMENT in ("development", "local"):
                logger.error(f"HTTPException error: {str(e)}")
                traceback.print_exc()
            return JSONResponse(
                status_code=e.status_code,
                content={"is_error": True, "status_code": e.status_code, "message": str(e.detail)},
            )

        except RequestValidationError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                logger.error(f"Request validation error: {str(e)}")
                traceback.print_exc()
            return JSONResponse(
                status_code=422,
                content={"is_error": True, "status_code": 422, "message": e.errors()},
            )

        except ValidationError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                logger.error(f"Validation error: {str(e)}")
            return JSONResponse(
                status_code=422,
                content={"is_error": True, "status_code": 422, "message": e.errors()},
            )

        except ExpiredSignatureError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                logger.error(f"Authentication error: {str(e)}")
            status = str(e.args[0]) if e.args else "401"
            message = str(e.args[1]) if len(e.args) > 1 else "token_has_expired"

            return JSONResponse(
                status_code=401,
                content={
                    "is_error": True,
                    "status_code": status,
                    "message": message,
                },
            )

        except JWTError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                logger.error(f"Authentication error: {str(e)}")
                traceback.print_exc()

            status = str(e.args[0]) if e.args else "401"
            message = str(e.args[1]) if len(e.args) > 1 else "invalid_token"

            return JSONResponse(
                status_code=401,
                content={
                    "is_error": True,
                    "status_code": status,
                    "message": message,
                },
            )

        except SQLAlchemyError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                logger.error(f"Database error: {str(e)}")
                traceback.print_exc()
            status = str(e.args[0]) if e.args else "500"
            message = str(e.args[1]) if len(e.args) > 1 else "Database error"

            return JSONResponse(
                status_code=500,
                content={"is_error": True, "status_code": status, "message": message},
            )

        except Exception as e:
            if settings.ENVIRONMENT in ("development", "local"):
                logger.error(f"Unexpected error: {str(e)}")
                traceback.print_exc()

            status = str(e.args[0]) if e.args else "500"
            message = str(e.args[1]) if len(e.args) > 1 else "Internal_server_error"

            return JSONResponse(
                status_code=500,
                content={
                    "is_error": True,
                    "status_code": status,
                    "message": message,
                },
            )
            
        except TimeoutError as e:
            if settings.ENVIRONMENT in ("development", "local"):
                logger.error(f"Timeout error for {request.method} {request.url}: {str(e)}")
                traceback.print_exc()
            return JSONResponse(
                status_code=408,
                content={
                    "is_error": True,
                    "status_code": 408,
                    "message": "request_timed_out",
                },
            )



    return wrapper

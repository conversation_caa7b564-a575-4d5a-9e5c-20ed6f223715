# main.py

from contextlib import asynccontextmanager

from fastapi import Fast<PERSON><PERSON>, Request
from fastapi_events.handlers.local import local_handler
from fastapi_events.middleware import Event<PERSON>andlerASGIMiddleware
from fastapi.middleware.cors import CORSMiddleware

######### Rate Limiter#######
from slowapi import Limiter
from slowapi.util import get_remote_address

from src.config import settings

from src.routes.auth import auth_router
from src.routes.contact_us import contact_router
from src.routes.general import general_router
from src.routes.profile import profile_router
from src.routes.whats_app import whats_app

from src.utils.cronjobs import start_cron_jobs
from src.utils.global_store import set_request
from src.utils.logger import TsLogger
from src.utils.whats_app import WhatsAppMessenger

logger = TsLogger(__name__)




# FastAPI lifespan event
@asynccontextmanager
async def lifespan(application: FastAPI):
    try:
        # start_cron_jobs()
        messenger = await WhatsAppMessenger.get_instance()
        application.state.whatsapp_messenger = messenger
        import asyncio
        asyncio.create_task(messenger.run())
        yield
    finally:
        pass


common_args = {
    "lifespan": lifespan,
    "title": "Mr.John - Course server",
    "description": "Mr.John - Course server - Courses and assessments",
    "version": settings.APP_VERSION,
    "root_path": '/api/v1',
}

common_args.update({
    "openapi_url": "/openapi.json" if settings.ENVIRONMENT != 'production' else "",
    "docs_url": "/docs" if settings.ENVIRONMENT != 'production' else "",
})

app = FastAPI(**common_args)


############Defined the rate limit middleware##########

limiter = Limiter(key_func=get_remote_address)

app.state.limiter = limiter  # type: ignore[attr-defined]

if settings.ENVIRONMENT == 'production':
    cors = settings.BACKEND_CORS_ORIGINS
else:
    cors = ["*"]


app.add_middleware(
    CORSMiddleware,
    allow_origins=cors,  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)
app.add_middleware(EventHandlerASGIMiddleware, handlers=[local_handler])


@app.middleware("http")
async def set_global_data(request: Request, call_next):
    # Set request data in the global storage
    set_request(request)
    response = await call_next(request)
    response.headers["X-Robots-Tag"] = "noindex, nofollow"  # Add the X-Robots-Tag header to disallow indexing
    return response


@app.get("/")
async def root():
    return {
        "status": settings.APP_STATUS,
        "version": settings.APP_VERSION,
    }


@app.get("/health", tags=["Health"])
def health_check():
    return {"status": "BackEnd is running"}


app.include_router(router=general_router)
app.include_router(router=auth_router)
app.include_router(router=profile_router)
app.include_router(router=contact_router)
app.include_router(router=whats_app)
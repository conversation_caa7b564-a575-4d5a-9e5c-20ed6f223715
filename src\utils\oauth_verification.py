# utils/oauth_verification.py
import httpx


from src.config import settings


async def verify_google_token(token: str) -> dict | None:
    url = f"https://oauth2.googleapis.com/tokeninfo?id_token={token}"

    async with httpx.AsyncClient() as client:
        res = await client.get(url)

    if res.status_code != 200:
        return None

    data = res.json()

    if data.get("aud") != settings.GOOGLE_CLIENT_ID:
        return None

    if not data.get("email_verified", False):
        return None

    if data.get("iss") not in ["https://accounts.google.com", "accounts.google.com"]:
        return None

    return {
        "email": data.get("email"),
        "first_name": data.get("given_name"),
        "last_name": data.get("family_name"),
        "profile_picture": data.get("picture"),
    }

async def verify_facebook_token(token: str) -> dict | None:
    app_token = f"{settings.FACEBOOK_APP_ID}|{settings.FACEBOOK_APP_SECRET}"
    debug_url = f"https://graph.facebook.com/debug_token?input_token={token}&access_token={app_token}"

    async with httpx.AsyncClient() as client:
        debug_res = await client.get(debug_url)
        if debug_res.status_code != 200:
            return None
        debug_data = debug_res.json().get("data", {})
        if not debug_data.get("is_valid") or debug_data.get("app_id") != settings.FACEBOOK_APP_ID:
            return None  # Token invalid or not for your app

        # Then fetch user info
        user_url = f"https://graph.facebook.com/me?fields=id,first_name,last_name,email,picture.type(large)&access_token={token}"
        user_res = await client.get(user_url)
        if user_res.status_code != 200:
            return None
        data = user_res.json()
        return {
            "email": data.get("email"),
            "first_name": data.get("first_name"),
            "last_name": data.get("last_name"),
            "profile_picture": data.get("picture", {}).get("data", {}).get("url"),
        }

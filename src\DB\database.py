# app/DB/database.py
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base

from src.config import settings

# Define your database connection URL (change to async URL)
SQLALCHEMY_DATABASE_URL = settings.DATABASE_URL.replace('postgresql://', 'postgresql+asyncpg://')

# Create a new async engine instance
engine = create_async_engine(
    SQLALCHEMY_DATABASE_URL,
    pool_pre_ping=True,
    pool_size=int(settings.POOL_SIZE),
    max_overflow=int(settings.MAX_OVERFLOW),
    pool_timeout=int(settings.POOL_TIMEOUT),
    pool_recycle=int(settings.POOL_RECYCLE),
    echo_pool=True,
    # echo=True
)

# Configure async_sessionmaker to establish new async sessions
AsyncSessionLocal = async_sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    expire_on_commit=False,
    class_=AsyncSession
)

# Declare the base class for all models
Base = declarative_base()

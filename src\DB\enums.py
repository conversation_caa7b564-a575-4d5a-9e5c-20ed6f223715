from enum import Enum


class UserGender(str, Enum):
    MALE = 'MALE'
    FEMALE = 'FEMALE'
    OTHER = 'OTHER'


class UserRole(str, Enum):
    USER = "USER"
    PARENT = "PARENT"
    ADMIN = "ADMIN"


class AuthProvider(str, Enum):
    LOCAL = "LOCAL"
    GOOGLE = "GOOGLE"
    FACEBOOK = "FACEBOOK"

class AssessmentType(str, Enum):
    MOCK = "MOCK"
    REal = "REAL"
    QUIZ = "QUIZ"
    
class CourseContentType(str, Enum):
    TEXT = "text"
    VIDEO = "video"
    ASSESSMENT = "assessment"

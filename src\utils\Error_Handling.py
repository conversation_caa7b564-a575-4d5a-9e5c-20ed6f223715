from enum import Enum


class ErrorCode(str, Enum):
        LOGIN_INVALID_ERROR = "login_invalid_error"
        OLD_USER = "old_user"
        EMAIL_NOT_CONFIRM = "email_not_confirmed"
        INVALID_VERIFICATION_CODE = "invalid_verification_code"
        INVALID_SUBMISSION_ID = "invalid_submission_id"
        FAILED_TO_SEND_RESPONSE = "failed_to_send_response"
        EXPIRED_VERIFICATION_CODE = "expired_verification_code"
        RECAPTCHA_FAILED = "recaptcha_failed"
        PASSWORDS_DONT_MATCH = "passwords_dont_match"
        OLD_PASSWORD_INCORRECT = "old_password_incorrect"
        PASSWORD_CHANGE_ERROR = "password_change_error"
        UNEXPECTED_ERROR = "unexpected_error"
        ACCOUNT_LOCKED = "account_locked"
        ACCOUNT_LOCKED_MINUTES = "account_locked_minutes"

        EXIST_EMAIL = "email_already_registered"
        USER_NOT_FOUND = "user_not_found"
        FAILED_TO_SEND_EMAIL = "failed_to_send_email"
        FAILED_TO_Update_EMAIL = "failed_to_update_email"
        FAILED_TO_GET_COUNTRIES = "failed_to_get_countries"
        EMAIL_ERROR = "email_does_not_belong_to_account"
        LOGIN_INVALID_password_ERROR = "login_invalid_password_error"
        NOT_ADMIN = "ADMIN_ONLY"

        ERROR_SYNCING_USER = "error_syncing_user"
        INVALID_JSON_RESPONSE_MICROSERVICE = "invalid_json_response_from_microservice"
        ERROR_SYNCING_USERS_MICROSERVICES = "error_syncing_users_with_microservices"
        INTERNAL_ERROR_SYNCING_USER_MICROSERVICES = "internal_error_syncing_user_with_microservices"
        
        INTERNAL_SERVER_ERROR = 'internal_server_error'
        UNEXPECTED_ERROR = 'unexpected_error'
        INVALID_AUTHORIZATION_HEADER = 'invalid_authorization_header'
        INVALID_CREDENTIALS = 'invalid_credentials'
        USER_NOT_FOUND = 'user_not_found'
        ACCESS_TOKEN_NOT_EXPIRED = 'access_token_not_expired'
        ACCESS_TOKEN_EXPIRED = 'access_token_expired'
        REFRESH_TOKEN_EXPIRED = 'refresh_token_expired'
        INVALID_REFRESH_TOKEN = 'invalid_refresh_token'
        INVALID_ACCESS_TOKEN = 'invalid_access_token'
        NO_REFRESH_TOKEN_PROVIDED = 'no_refresh_token_provided'
        ERROR_DECODING_TOKEN = "Error_decoding_token"
        
        USER_NOT_FOUND = "user_not_found"
        DIMENSION_NOT_FOUND = "dimension_not_found"
        ASSESSMENT_NOT_FOUND = "assessment_not_found"
        ALLOW_TO_CONTENT_MANAGER_ONLY = "allow_to_content_manger_only"
        ASSESSMENT_TYPES_NOT_CREATED = "assessment_types_not_created"
        ASSESSMENT_PROFILES_NOT_FOUND = "assessment_profiles_not_found"
        ASSESSMENT_PROFILE_NOT_FOUND = "assessment_profile_not_found"
        ERROR_ON_DATABASE_CONNECTION = "error_in_database_connection"
        UNEXPECTED_ERROR = "unexpected_error"
        QUESTION_NOT_FOUND = "question_not_found"
        OPTION_NOT_FOUND = "option_not_found"
        OPTION_SCORE_NOT_FOUND = "option_score_not_found"
        SESSION_NOT_FOUND = "session_not_found"
        SESSION_ALREADY_COMPLETED = "session_already_completed"
        NOT_ALL_QUESTIONS_ANSWERED = "not_all_questions_answered"
        RESPONSES_ALREADY_SUBMITTED = "responses_already_submitted"
        THIS_USER_NOT_FOUND = "this_user_not_found"
        THIS_SESSION_NOT_BELONG_TO_THIS_USER = "this_session_not_belong_to_this_user"
        COULD_NOT_CALCULATE_RESULT_PROFILE = "could_not_calculate_result_profile"
        
        SESSION_ALREADY_EXISTS = "session_already_exists"
        SESSION_NOT_FOUND = "session_not_found"
        QR_NOT_AVAILABLE = "qr_not_available"
        DB_ERROR = "database_error"
        API_CREATE_FAILED = "api_create_failed"
        API_QR_FAILED = "api_qr_failed"
        API_STATUS_FAILED = "api_status_failed"
        API_DELETE_FAILED = "api_delete_failed"
        API_DISCONNECT_FAILED = "api_disconnect_failed"
        API_CONNECT_FAILED = "api_connect_failed"
        SET_ACTIVE_FAILED = "set_active_failed"
        NO_USER_SESSIONS = "no_user_sessions"
